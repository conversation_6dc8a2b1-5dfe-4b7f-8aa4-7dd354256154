import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/quote.dart';
import '../constants/app_constants.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), AppConstants.databaseName);
    
    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _createDatabase,
      onUpgrade: _upgradeDatabase,
    );
  }

  Future<void> _createDatabase(Database db, int version) async {
    await db.execute('''
      CREATE TABLE ${AppConstants.quotesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        text TEXT NOT NULL,
        author TEXT NOT NULL,
        category TEXT NOT NULL,
        dateAdded INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE ${AppConstants.favoritesTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        quoteId INTEGER NOT NULL,
        dateAdded INTEGER NOT NULL,
        FOREIGN KEY (quoteId) REFERENCES ${AppConstants.quotesTable} (id)
      )
    ''');

    // Insert initial quotes
    await _insertInitialQuotes(db);
  }

  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < 2) {
      // Add new columns or tables for future versions
    }
  }

  Future<void> _insertInitialQuotes(Database db) async {
    final initialQuotes = [
      Quote(text: "The only way to do great work is to love what you do.", author: "Steve Jobs", category: "Motivational"),
      Quote(text: "Life is what happens to you while you're busy making other plans.", author: "John Lennon", category: "Life"),
      Quote(text: "The future belongs to those who believe in the beauty of their dreams.", author: "Eleanor Roosevelt", category: "Dreams"),
      Quote(text: "It is during our darkest moments that we must focus to see the light.", author: "Aristotle", category: "Inspirational"),
      Quote(text: "The only impossible journey is the one you never begin.", author: "Tony Robbins", category: "Motivational"),
      Quote(text: "In the end, we will remember not the words of our enemies, but the silence of our friends.", author: "Martin Luther King Jr.", category: "Friendship"),
      Quote(text: "Success is not final, failure is not fatal: it is the courage to continue that counts.", author: "Winston Churchill", category: "Success"),
      Quote(text: "The way to get started is to quit talking and begin doing.", author: "Walt Disney", category: "Motivational"),
      Quote(text: "Don't let yesterday take up too much of today.", author: "Will Rogers", category: "Life"),
      Quote(text: "You learn more from failure than from success. Don't let it stop you. Failure builds character.", author: "Unknown", category: "Wisdom"),
      Quote(text: "If you are working on something that you really care about, you don't have to be pushed. The vision pulls you.", author: "Steve Jobs", category: "Leadership"),
      Quote(text: "People who are crazy enough to think they can change the world, are the ones who do.", author: "Rob Siltanen", category: "Dreams"),
      Quote(text: "Happiness is not something ready made. It comes from your own actions.", author: "Dalai Lama", category: "Happiness"),
      Quote(text: "The only person you are destined to become is the person you decide to be.", author: "Ralph Waldo Emerson", category: "Life"),
      Quote(text: "Believe you can and you're halfway there.", author: "Theodore Roosevelt", category: "Motivational"),
      Quote(text: "The best time to plant a tree was 20 years ago. The second best time is now.", author: "Chinese Proverb", category: "Wisdom"),
      Quote(text: "Your limitation—it's only your imagination.", author: "Unknown", category: "Inspirational"),
      Quote(text: "Great things never come from comfort zones.", author: "Unknown", category: "Courage"),
      Quote(text: "Dream it. Wish it. Do it.", author: "Unknown", category: "Dreams"),
      Quote(text: "Success doesn't just find you. You have to go out and get it.", author: "Unknown", category: "Success"),
      Quote(text: "The harder you work for something, the greater you'll feel when you achieve it.", author: "Unknown", category: "Success"),
      Quote(text: "Dream bigger. Do bigger.", author: "Unknown", category: "Dreams"),
      Quote(text: "Don't stop when you're tired. Stop when you're done.", author: "Unknown", category: "Motivational"),
      Quote(text: "Wake up with determination. Go to bed with satisfaction.", author: "Unknown", category: "Motivational"),
      Quote(text: "Do something today that your future self will thank you for.", author: "Sean Patrick Flanery", category: "Life"),
      Quote(text: "Being deeply loved by someone gives you strength, while loving someone deeply gives you courage.", author: "Lao Tzu", category: "Love"),
      Quote(text: "The best thing to hold onto in life is each other.", author: "Audrey Hepburn", category: "Love"),
      Quote(text: "Love is composed of a single soul inhabiting two bodies.", author: "Aristotle", category: "Love"),
      Quote(text: "A friend is someone who knows all about you and still loves you.", author: "Elbert Hubbard", category: "Friendship"),
      Quote(text: "Friendship is the only cement that will ever hold the world together.", author: "Woodrow Wilson", category: "Friendship"),
    ];

    for (final quote in initialQuotes) {
      await db.insert(AppConstants.quotesTable, quote.toMap());
    }
  }

  // Quote operations
  Future<List<Quote>> getAllQuotes() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(AppConstants.quotesTable);
    return List.generate(maps.length, (i) => Quote.fromMap(maps[i]));
  }

  Future<List<Quote>> getQuotesByCategory(String category) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.quotesTable,
      where: 'category = ?',
      whereArgs: [category],
    );
    return List.generate(maps.length, (i) => Quote.fromMap(maps[i]));
  }

  Future<List<Quote>> searchQuotes(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.quotesTable,
      where: 'text LIKE ? OR author LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
    );
    return List.generate(maps.length, (i) => Quote.fromMap(maps[i]));
  }

  Future<Quote> getRandomQuote() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      'SELECT * FROM ${AppConstants.quotesTable} ORDER BY RANDOM() LIMIT 1'
    );
    if (maps.isNotEmpty) {
      return Quote.fromMap(maps.first);
    }
    throw Exception('No quotes found');
  }

  Future<int> insertQuote(Quote quote) async {
    final db = await database;
    return await db.insert(AppConstants.quotesTable, quote.toMap());
  }

  Future<void> updateQuote(Quote quote) async {
    final db = await database;
    await db.update(
      AppConstants.quotesTable,
      quote.toMap(),
      where: 'id = ?',
      whereArgs: [quote.id],
    );
  }

  Future<void> deleteQuote(int id) async {
    final db = await database;
    await db.delete(
      AppConstants.quotesTable,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Favorites operations
  Future<List<Quote>> getFavoriteQuotes() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT q.* FROM ${AppConstants.quotesTable} q
      INNER JOIN ${AppConstants.favoritesTable} f ON q.id = f.quoteId
      ORDER BY f.dateAdded DESC
    ''');
    return List.generate(maps.length, (i) => Quote.fromMap(maps[i]));
  }

  Future<bool> isQuoteFavorite(int quoteId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      AppConstants.favoritesTable,
      where: 'quoteId = ?',
      whereArgs: [quoteId],
    );
    return maps.isNotEmpty;
  }

  Future<void> addToFavorites(int quoteId) async {
    final db = await database;
    await db.insert(AppConstants.favoritesTable, {
      'quoteId': quoteId,
      'dateAdded': DateTime.now().millisecondsSinceEpoch,
    });
  }

  Future<void> removeFromFavorites(int quoteId) async {
    final db = await database;
    await db.delete(
      AppConstants.favoritesTable,
      where: 'quoteId = ?',
      whereArgs: [quoteId],
    );
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
