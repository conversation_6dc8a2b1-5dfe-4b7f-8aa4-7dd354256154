import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quote_provider.dart';
import '../widgets/quote_card.dart';
import '../widgets/loading_widget.dart';
import '../widgets/empty_state_widget.dart';
import '../widgets/custom_app_bar.dart';
import '../constants/app_constants.dart';
import '../utils/app_utils.dart';
import '../models/quote.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  late TextEditingController _searchController;
  late FocusNode _focusNode;
  String _currentQuery = '';
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _focusNode = FocusNode();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _currentQuery = query;
      _isSearching = query.isNotEmpty;
    });
    
    if (query.isNotEmpty) {
      context.read<QuoteProvider>().searchQuotes(query);
    } else {
      context.read<QuoteProvider>().loadQuotes();
    }
  }

  void _onClearSearch() {
    _searchController.clear();
    _onSearchChanged('');
    _focusNode.requestFocus();
  }

  void _onFavoriteToggle(Quote quote) async {
    await context.read<QuoteProvider>().toggleFavorite(quote);
    
    if (mounted) {
      final isFavorite = await context.read<QuoteProvider>().isQuoteFavorite(quote);
      AppUtils.showSnackBar(
        context,
        isFavorite ? AppStrings.addedToFavorites : AppStrings.removedFromFavorites,
        icon: isFavorite ? Icons.favorite : Icons.favorite_border,
      );
    }
  }

  void _onShareQuote(Quote quote) async {
    final RenderBox? box = context.findRenderObject() as RenderBox?;
    final Rect sharePositionOrigin = box != null
        ? box.localToGlobal(Offset.zero) & box.size
        : Rect.zero;
    
    await AppUtils.shareQuote(quote, sharePositionOrigin: sharePositionOrigin);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: SearchAppBar(
        hintText: AppStrings.searchHint,
        controller: _searchController,
        onChanged: _onSearchChanged,
        onClear: _onClearSearch,
        onBack: () => Navigator.of(context).pop(),
      ),
      body: Column(
        children: [
          // Search suggestions/filters
          if (!_isSearching) _buildSearchSuggestions(),
          
          // Search results
          Expanded(
            child: Consumer<QuoteProvider>(
              builder: (context, quoteProvider, child) {
                if (quoteProvider.isLoading) {
                  return const LoadingWidget(message: 'Searching quotes...');
                }

                if (_isSearching && quoteProvider.filteredQuotes.isEmpty) {
                  return NoSearchResultsWidget(
                    searchQuery: _currentQuery,
                    onClearSearch: _onClearSearch,
                  );
                }

                if (!_isSearching && quoteProvider.quotes.isEmpty) {
                  return const EmptyStateWidget(
                    icon: Icons.search,
                    title: 'Start Searching',
                    message: 'Enter keywords or author names to find quotes.',
                  );
                }

                final quotesToShow = _isSearching 
                    ? quoteProvider.filteredQuotes 
                    : quoteProvider.quotes;

                return ListView.builder(
                  padding: const EdgeInsets.only(
                    bottom: AppConstants.defaultPadding,
                  ),
                  itemCount: quotesToShow.length,
                  itemBuilder: (context, index) {
                    final quote = quotesToShow[index];
                    return QuoteCard(
                      quote: quote,
                      animationIndex: index,
                      onTap: () {
                        context.read<QuoteProvider>().setCurrentQuoteByIndex(index);
                      },
                      onFavoriteToggle: () => _onFavoriteToggle(quote),
                      onShare: () => _onShareQuote(quote),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSuggestions() {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Search by Category',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: AppConstants.quoteCategories
                .where((category) => category != 'All')
                .map((category) => _buildCategoryChip(category))
                .toList(),
          ),
          
          const SizedBox(height: AppConstants.defaultPadding),
          
          Text(
            'Popular Authors',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              'Steve Jobs',
              'Albert Einstein',
              'Maya Angelou',
              'Winston Churchill',
              'Mark Twain',
              'Oscar Wilde',
            ].map((author) => _buildAuthorChip(author)).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(String category) {
    final theme = Theme.of(context);
    final categoryColor = AppUtils.getCategoryColor(category);
    
    return ActionChip(
      label: Text(category),
      avatar: Icon(
        AppUtils.getCategoryIcon(category),
        size: 16,
        color: categoryColor,
      ),
      onPressed: () {
        _searchController.text = category.toLowerCase();
        _onSearchChanged(category.toLowerCase());
      },
      backgroundColor: categoryColor.withOpacity(0.1),
      labelStyle: TextStyle(
        color: categoryColor,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  Widget _buildAuthorChip(String author) {
    final theme = Theme.of(context);
    
    return ActionChip(
      label: Text(author),
      avatar: const Icon(
        Icons.person,
        size: 16,
      ),
      onPressed: () {
        _searchController.text = author;
        _onSearchChanged(author);
      },
      backgroundColor: theme.colorScheme.secondaryContainer.withOpacity(0.5),
      labelStyle: TextStyle(
        color: theme.colorScheme.onSecondaryContainer,
        fontWeight: FontWeight.w500,
      ),
    );
  }
}
