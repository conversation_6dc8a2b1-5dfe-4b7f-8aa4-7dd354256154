import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quote_provider.dart';
import '../widgets/quote_card.dart';
import '../widgets/loading_widget.dart';
import '../widgets/empty_state_widget.dart';
import '../widgets/custom_app_bar.dart';
import '../constants/app_constants.dart';
import '../utils/app_utils.dart';
import '../models/quote.dart';

class CategoryQuotesScreen extends StatefulWidget {
  final String category;

  const CategoryQuotesScreen({
    super.key,
    required this.category,
  });

  @override
  State<CategoryQuotesScreen> createState() => _CategoryQuotesScreenState();
}

class _CategoryQuotesScreenState extends State<CategoryQuotesScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<QuoteProvider>().loadQuotesByCategory(widget.category);
    });
  }

  Future<void> _onRefresh() async {
    await context.read<QuoteProvider>().loadQuotesByCategory(widget.category);
  }

  void _onFavoriteToggle(Quote quote) async {
    await context.read<QuoteProvider>().toggleFavorite(quote);
    
    if (mounted) {
      final isFavorite = await context.read<QuoteProvider>().isQuoteFavorite(quote);
      AppUtils.showSnackBar(
        context,
        isFavorite ? AppStrings.addedToFavorites : AppStrings.removedFromFavorites,
        icon: isFavorite ? Icons.favorite : Icons.favorite_border,
      );
    }
  }

  void _onShareQuote(Quote quote) async {
    final RenderBox? box = context.findRenderObject() as RenderBox?;
    final Rect sharePositionOrigin = box != null
        ? box.localToGlobal(Offset.zero) & box.size
        : Rect.zero;
    
    await AppUtils.shareQuote(quote, sharePositionOrigin: sharePositionOrigin);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final categoryColor = AppUtils.getCategoryColor(widget.category);
    
    return Scaffold(
      body: Consumer<QuoteProvider>(
        builder: (context, quoteProvider, child) {
          return CustomScrollView(
            slivers: [
              // Custom App Bar with category theme
              SliverAppBar(
                expandedHeight: 120,
                floating: true,
                pinned: true,
                backgroundColor: categoryColor,
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(
                    widget.category,
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  background: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          categoryColor,
                          categoryColor.withOpacity(0.8),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: SafeArea(
                      child: Padding(
                        padding: const EdgeInsets.all(AppConstants.defaultPadding),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  AppUtils.getCategoryIcon(widget.category),
                                  color: Colors.white,
                                  size: 24,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '${quoteProvider.filteredQuotes.length} quotes',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color: Colors.white.withOpacity(0.9),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                leading: IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
                ),
              ),

              // Content
              if (quoteProvider.isLoading)
                const SliverFillRemaining(
                  child: LoadingWidget(message: 'Loading quotes...'),
                )
              else if (quoteProvider.error != null)
                SliverFillRemaining(
                  child: ErrorStateWidget(
                    message: quoteProvider.error!,
                    onActionPressed: _onRefresh,
                  ),
                )
              else if (quoteProvider.filteredQuotes.isEmpty)
                SliverFillRemaining(
                  child: EmptyStateWidget(
                    icon: AppUtils.getCategoryIcon(widget.category),
                    title: 'No Quotes Found',
                    message: 'No quotes available in ${widget.category} category.',
                    iconColor: categoryColor,
                  ),
                )
              else
                SliverPadding(
                  padding: const EdgeInsets.only(
                    bottom: AppConstants.defaultPadding,
                  ),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final quote = quoteProvider.filteredQuotes[index];
                        return QuoteCard(
                          quote: quote,
                          animationIndex: index,
                          onTap: () {
                            context.read<QuoteProvider>().setCurrentQuoteByIndex(index);
                          },
                          onFavoriteToggle: () => _onFavoriteToggle(quote),
                          onShare: () => _onShareQuote(quote),
                        );
                      },
                      childCount: quoteProvider.filteredQuotes.length,
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
