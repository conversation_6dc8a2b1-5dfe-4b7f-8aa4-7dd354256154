import 'package:flutter/material.dart';
import '../services/preferences_service.dart';
import '../services/notification_service.dart';

class SettingsProvider with ChangeNotifier {
  final PreferencesService _preferencesService = PreferencesService();
  final NotificationService _notificationService = NotificationService();

  bool _notificationsEnabled = true;
  TimeOfDay _notificationTime = const TimeOfDay(hour: 9, minute: 0);
  bool _isLoading = false;

  // Getters
  bool get notificationsEnabled => _notificationsEnabled;
  TimeOfDay get notificationTime => _notificationTime;
  bool get isLoading => _isLoading;

  Future<void> initialize() async {
    _setLoading(true);
    
    _notificationsEnabled = _preferencesService.getNotificationsEnabled();
    _notificationTime = _preferencesService.getNotificationTime();
    
    _setLoading(false);
  }

  Future<void> setNotificationsEnabled(bool enabled) async {
    _notificationsEnabled = enabled;
    await _preferencesService.setNotificationsEnabled(enabled);
    
    if (enabled) {
      final hasPermission = await _notificationService.requestPermissions();
      if (hasPermission) {
        await _notificationService.scheduleDailyNotification();
      } else {
        _notificationsEnabled = false;
        await _preferencesService.setNotificationsEnabled(false);
      }
    } else {
      await _notificationService.cancelAllNotifications();
    }
    
    notifyListeners();
  }

  Future<void> setNotificationTime(TimeOfDay time) async {
    _notificationTime = time;
    await _preferencesService.setNotificationTime(time);
    
    // Reschedule notification with new time if notifications are enabled
    if (_notificationsEnabled) {
      await _notificationService.scheduleDailyNotification();
    }
    
    notifyListeners();
  }

  Future<bool> requestNotificationPermissions() async {
    return await _notificationService.requestPermissions();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
