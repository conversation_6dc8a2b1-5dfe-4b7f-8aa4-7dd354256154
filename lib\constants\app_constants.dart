import 'package:flutter/material.dart';

class AppConstants {
  // App Info
  static const String appName = 'Daily Quotes';
  static const String appVersion = '1.0.0';
  
  // Database
  static const String databaseName = 'quotes_database.db';
  static const int databaseVersion = 1;
  static const String quotesTable = 'quotes';
  static const String favoritesTable = 'favorites';
  
  // SharedPreferences Keys
  static const String keyThemeMode = 'theme_mode';
  static const String keyNotificationsEnabled = 'notifications_enabled';
  static const String keyNotificationTime = 'notification_time';
  static const String keyLastQuoteDate = 'last_quote_date';
  static const String keyCurrentQuoteIndex = 'current_quote_index';
  static const String keyFirstLaunch = 'first_launch';
  
  // Quote Categories
  static const List<String> quoteCategories = [
    'All',
    'Motivational',
    'Love',
    'Success',
    'Wisdom',
    'Life',
    'Happiness',
    'Inspirational',
    'Friendship',
    'Dreams',
    'Leadership',
    'Courage',
  ];
  
  // Category Icons
  static const Map<String, IconData> categoryIcons = {
    'All': Icons.all_inclusive,
    'Motivational': Icons.fitness_center,
    'Love': Icons.favorite,
    'Success': Icons.star,
    'Wisdom': Icons.lightbulb,
    'Life': Icons.nature_people,
    'Happiness': Icons.sentiment_very_satisfied,
    'Inspirational': Icons.auto_awesome,
    'Friendship': Icons.people,
    'Dreams': Icons.bedtime,
    'Leadership': Icons.trending_up,
    'Courage': Icons.shield,
  };
  
  // Category Colors
  static const Map<String, Color> categoryColors = {
    'All': Colors.blue,
    'Motivational': Colors.orange,
    'Love': Colors.pink,
    'Success': Colors.green,
    'Wisdom': Colors.purple,
    'Life': Colors.teal,
    'Happiness': Colors.yellow,
    'Inspirational': Colors.indigo,
    'Friendship': Colors.cyan,
    'Dreams': Colors.deepPurple,
    'Leadership': Colors.red,
    'Courage': Colors.amber,
  };
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;
  
  // Notification
  static const int dailyNotificationId = 1;
  static const String notificationChannelId = 'daily_quotes_channel';
  static const String notificationChannelName = 'Daily Quotes';
  static const String notificationChannelDescription = 'Daily inspirational quotes notifications';
}

class AppStrings {
  // Navigation
  static const String home = 'Home';
  static const String categories = 'Categories';
  static const String favorites = 'Favorites';
  static const String settings = 'Settings';
  static const String search = 'Search';
  
  // Actions
  static const String share = 'Share';
  static const String copy = 'Copy';
  static const String favorite = 'Favorite';
  static const String unfavorite = 'Remove from favorites';
  static const String refresh = 'Refresh';
  
  // Messages
  static const String noFavorites = 'No favorite quotes yet.\nStart adding some!';
  static const String noSearchResults = 'No quotes found.\nTry different keywords.';
  static const String errorLoadingQuotes = 'Error loading quotes.\nPlease try again.';
  static const String quoteCopied = 'Quote copied to clipboard';
  static const String addedToFavorites = 'Added to favorites';
  static const String removedFromFavorites = 'Removed from favorites';
  
  // Settings
  static const String darkMode = 'Dark Mode';
  static const String notifications = 'Daily Notifications';
  static const String notificationTime = 'Notification Time';
  static const String about = 'About';
  static const String version = 'Version';
  
  // Search
  static const String searchHint = 'Search quotes or authors...';
  static const String searchByAuthor = 'Search by author';
  static const String searchByKeyword = 'Search by keyword';
}
