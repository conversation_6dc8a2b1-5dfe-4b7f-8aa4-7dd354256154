import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../models/quote.dart';
import '../utils/app_utils.dart';
import '../constants/app_constants.dart';

class QuoteCard extends StatefulWidget {
  final Quote quote;
  final VoidCallback? onTap;
  final VoidCallback? onFavoriteToggle;
  final VoidCallback? onShare;
  final bool showActions;
  final int? animationIndex;
  final bool isCompact;

  const QuoteCard({
    super.key,
    required this.quote,
    this.onTap,
    this.onFavoriteToggle,
    this.onShare,
    this.showActions = true,
    this.animationIndex,
    this.isCompact = false,
  });

  @override
  State<QuoteCard> createState() => _QuoteCardState();
}

class _QuoteCardState extends State<QuoteCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppConstants.shortAnimation,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _animationController.forward();
    AppUtils.lightHaptic();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  void _onTapCancel() {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final gradientColors = AppUtils.getRandomGradient();

    Widget card = AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            onTap: widget.onTap,
            child: Container(
              margin: EdgeInsets.symmetric(
                horizontal: AppConstants.defaultPadding,
                vertical: widget.isCompact ? AppConstants.smallPadding : AppConstants.smallPadding,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                gradient: LinearGradient(
                  colors: gradientColors,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: gradientColors.first.withOpacity(0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Container(
                padding: EdgeInsets.all(
                  widget.isCompact ? AppConstants.defaultPadding : AppConstants.largePadding,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  color: theme.colorScheme.surface.withOpacity(0.95),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Category chip
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppUtils.getCategoryColor(widget.quote.category)
                                .withOpacity(0.2),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                AppUtils.getCategoryIcon(widget.quote.category),
                                size: 14,
                                color: AppUtils.getCategoryColor(widget.quote.category),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                widget.quote.category,
                                style: theme.textTheme.labelSmall?.copyWith(
                                  color: AppUtils.getCategoryColor(widget.quote.category),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const Spacer(),
                        if (widget.showActions)
                          IconButton(
                            onPressed: widget.onFavoriteToggle,
                            icon: Icon(
                              widget.quote.isFavorite
                                  ? Icons.favorite
                                  : Icons.favorite_border,
                              color: widget.quote.isFavorite
                                  ? Colors.red
                                  : theme.colorScheme.onSurfaceVariant,
                            ),
                            iconSize: 20,
                          ),
                      ],
                    ),
                    
                    SizedBox(height: widget.isCompact ? 12 : 16),
                    
                    // Quote text
                    Text(
                      '"${widget.quote.text}"',
                      style: widget.isCompact
                          ? theme.textTheme.bodyMedium?.copyWith(
                              fontStyle: FontStyle.italic,
                              height: 1.4,
                            )
                          : theme.textTheme.bodyLarge?.copyWith(
                              fontStyle: FontStyle.italic,
                              height: 1.5,
                              fontSize: 16,
                            ),
                    ),
                    
                    SizedBox(height: widget.isCompact ? 8 : 12),
                    
                    // Author and actions
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            '— ${widget.quote.author}',
                            style: widget.isCompact
                                ? theme.textTheme.labelMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: theme.colorScheme.primary,
                                  )
                                : theme.textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: theme.colorScheme.primary,
                                  ),
                          ),
                        ),
                        if (widget.showActions) ...[
                          IconButton(
                            onPressed: () async {
                              await AppUtils.copyQuoteToClipboard(widget.quote);
                              if (context.mounted) {
                                AppUtils.showSnackBar(
                                  context,
                                  AppStrings.quoteCopied,
                                  icon: Icons.copy,
                                );
                              }
                            },
                            icon: const Icon(Icons.copy),
                            iconSize: 18,
                            tooltip: AppStrings.copy,
                          ),
                          IconButton(
                            onPressed: widget.onShare,
                            icon: const Icon(Icons.share),
                            iconSize: 18,
                            tooltip: AppStrings.share,
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );

    // Add staggered animation if index is provided
    if (widget.animationIndex != null) {
      return AnimationConfiguration.staggeredList(
        position: widget.animationIndex!,
        duration: AppConstants.mediumAnimation,
        child: SlideAnimation(
          verticalOffset: 50.0,
          child: FadeInAnimation(
            child: card,
          ),
        ),
      );
    }

    return card;
  }
}
