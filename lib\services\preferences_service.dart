import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

class PreferencesService {
  static final PreferencesService _instance = PreferencesService._internal();
  factory PreferencesService() => _instance;
  PreferencesService._internal();

  SharedPreferences? _prefs;

  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('PreferencesService not initialized. Call init() first.');
    }
    return _prefs!;
  }

  // Theme Mode
  Future<void> setThemeMode(ThemeMode themeMode) async {
    await prefs.setString(AppConstants.keyThemeMode, themeMode.name);
  }

  ThemeMode getThemeMode() {
    final themeModeString = prefs.getString(AppConstants.keyThemeMode);
    switch (themeModeString) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      case 'system':
      default:
        return ThemeMode.system;
    }
  }

  // Notifications
  Future<void> setNotificationsEnabled(bool enabled) async {
    await prefs.setBool(AppConstants.keyNotificationsEnabled, enabled);
  }

  bool getNotificationsEnabled() {
    return prefs.getBool(AppConstants.keyNotificationsEnabled) ?? true;
  }

  Future<void> setNotificationTime(TimeOfDay time) async {
    final timeString = '${time.hour}:${time.minute}';
    await prefs.setString(AppConstants.keyNotificationTime, timeString);
  }

  TimeOfDay getNotificationTime() {
    final timeString = prefs.getString(AppConstants.keyNotificationTime);
    if (timeString != null) {
      final parts = timeString.split(':');
      if (parts.length == 2) {
        final hour = int.tryParse(parts[0]);
        final minute = int.tryParse(parts[1]);
        if (hour != null && minute != null) {
          return TimeOfDay(hour: hour, minute: minute);
        }
      }
    }
    return const TimeOfDay(hour: 9, minute: 0); // Default to 9:00 AM
  }

  // Quote tracking
  Future<void> setLastQuoteDate(DateTime date) async {
    await prefs.setString(AppConstants.keyLastQuoteDate, date.toIso8601String());
  }

  DateTime? getLastQuoteDate() {
    final dateString = prefs.getString(AppConstants.keyLastQuoteDate);
    if (dateString != null) {
      return DateTime.tryParse(dateString);
    }
    return null;
  }

  Future<void> setCurrentQuoteIndex(int index) async {
    await prefs.setInt(AppConstants.keyCurrentQuoteIndex, index);
  }

  int getCurrentQuoteIndex() {
    return prefs.getInt(AppConstants.keyCurrentQuoteIndex) ?? 0;
  }

  // First launch
  Future<void> setFirstLaunch(bool isFirstLaunch) async {
    await prefs.setBool(AppConstants.keyFirstLaunch, isFirstLaunch);
  }

  bool isFirstLaunch() {
    return prefs.getBool(AppConstants.keyFirstLaunch) ?? true;
  }

  // Clear all preferences
  Future<void> clearAll() async {
    await prefs.clear();
  }

  // Check if today's quote has been shown
  bool shouldShowNewQuote() {
    final lastQuoteDate = getLastQuoteDate();
    if (lastQuoteDate == null) return true;
    
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final lastDate = DateTime(lastQuoteDate.year, lastQuoteDate.month, lastQuoteDate.day);
    
    return today.isAfter(lastDate);
  }

  // Mark today's quote as shown
  Future<void> markTodayQuoteShown() async {
    await setLastQuoteDate(DateTime.now());
  }
}
