import 'package:flutter/foundation.dart';
import '../models/quote.dart';
import '../services/database_service.dart';
import '../services/preferences_service.dart';

class QuoteProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final PreferencesService _preferencesService = PreferencesService();

  List<Quote> _quotes = [];
  List<Quote> _favoriteQuotes = [];
  List<Quote> _filteredQuotes = [];
  Quote? _currentQuote;
  String _selectedCategory = 'All';
  bool _isLoading = false;
  String? _error;
  int _currentIndex = 0;

  // Getters
  List<Quote> get quotes => _quotes;
  List<Quote> get favoriteQuotes => _favoriteQuotes;
  List<Quote> get filteredQuotes => _filteredQuotes;
  Quote? get currentQuote => _currentQuote;
  String get selectedCategory => _selectedCategory;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get currentIndex => _currentIndex;

  // Initialize provider
  Future<void> initialize() async {
    await loadQuotes();
    await loadFavorites();
    await loadCurrentQuote();
  }

  // Load all quotes
  Future<void> loadQuotes() async {
    try {
      _setLoading(true);
      _quotes = await _databaseService.getAllQuotes();
      _filteredQuotes = _quotes;
      _setError(null);
    } catch (e) {
      _setError('Failed to load quotes: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load quotes by category
  Future<void> loadQuotesByCategory(String category) async {
    try {
      _setLoading(true);
      _selectedCategory = category;
      
      if (category == 'All') {
        _filteredQuotes = _quotes;
      } else {
        _filteredQuotes = await _databaseService.getQuotesByCategory(category);
      }
      
      _setError(null);
    } catch (e) {
      _setError('Failed to load quotes by category: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Search quotes
  Future<void> searchQuotes(String query) async {
    try {
      _setLoading(true);
      
      if (query.isEmpty) {
        _filteredQuotes = _selectedCategory == 'All' 
            ? _quotes 
            : await _databaseService.getQuotesByCategory(_selectedCategory);
      } else {
        _filteredQuotes = await _databaseService.searchQuotes(query);
      }
      
      _setError(null);
    } catch (e) {
      _setError('Failed to search quotes: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load favorite quotes
  Future<void> loadFavorites() async {
    try {
      _favoriteQuotes = await _databaseService.getFavoriteQuotes();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load favorites: $e');
    }
  }

  // Toggle favorite status
  Future<void> toggleFavorite(Quote quote) async {
    try {
      if (quote.id == null) return;
      
      final isFavorite = await _databaseService.isQuoteFavorite(quote.id!);
      
      if (isFavorite) {
        await _databaseService.removeFromFavorites(quote.id!);
        _favoriteQuotes.removeWhere((q) => q.id == quote.id);
      } else {
        await _databaseService.addToFavorites(quote.id!);
        _favoriteQuotes.add(quote);
      }
      
      // Update current quote if it's the same
      if (_currentQuote?.id == quote.id) {
        _currentQuote = quote.copyWith(isFavorite: !isFavorite);
      }
      
      // Update quotes in lists
      _updateQuoteInLists(quote.copyWith(isFavorite: !isFavorite));
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to toggle favorite: $e');
    }
  }

  // Check if quote is favorite
  Future<bool> isQuoteFavorite(Quote quote) async {
    if (quote.id == null) return false;
    return await _databaseService.isQuoteFavorite(quote.id!);
  }

  // Load current quote (daily quote)
  Future<void> loadCurrentQuote() async {
    try {
      _currentIndex = _preferencesService.getCurrentQuoteIndex();
      
      if (_preferencesService.shouldShowNewQuote()) {
        // Get a new random quote for today
        _currentQuote = await _databaseService.getRandomQuote();
        await _preferencesService.markTodayQuoteShown();
      } else {
        // Use the stored index to get consistent quote for today
        if (_quotes.isNotEmpty) {
          final index = _currentIndex % _quotes.length;
          _currentQuote = _quotes[index];
        } else {
          _currentQuote = await _databaseService.getRandomQuote();
        }
      }
      
      // Check if current quote is favorite
      if (_currentQuote != null && _currentQuote!.id != null) {
        final isFavorite = await _databaseService.isQuoteFavorite(_currentQuote!.id!);
        _currentQuote = _currentQuote!.copyWith(isFavorite: isFavorite);
      }
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to load current quote: $e');
    }
  }

  // Get next quote
  Future<void> nextQuote() async {
    try {
      if (_filteredQuotes.isEmpty) return;
      
      _currentIndex = (_currentIndex + 1) % _filteredQuotes.length;
      _currentQuote = _filteredQuotes[_currentIndex];
      
      // Check if quote is favorite
      if (_currentQuote!.id != null) {
        final isFavorite = await _databaseService.isQuoteFavorite(_currentQuote!.id!);
        _currentQuote = _currentQuote!.copyWith(isFavorite: isFavorite);
      }
      
      await _preferencesService.setCurrentQuoteIndex(_currentIndex);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get next quote: $e');
    }
  }

  // Get previous quote
  Future<void> previousQuote() async {
    try {
      if (_filteredQuotes.isEmpty) return;
      
      _currentIndex = _currentIndex > 0 
          ? _currentIndex - 1 
          : _filteredQuotes.length - 1;
      _currentQuote = _filteredQuotes[_currentIndex];
      
      // Check if quote is favorite
      if (_currentQuote!.id != null) {
        final isFavorite = await _databaseService.isQuoteFavorite(_currentQuote!.id!);
        _currentQuote = _currentQuote!.copyWith(isFavorite: isFavorite);
      }
      
      await _preferencesService.setCurrentQuoteIndex(_currentIndex);
      notifyListeners();
    } catch (e) {
      _setError('Failed to get previous quote: $e');
    }
  }

  // Set current quote by index
  Future<void> setCurrentQuoteByIndex(int index) async {
    try {
      if (_filteredQuotes.isEmpty || index < 0 || index >= _filteredQuotes.length) {
        return;
      }
      
      _currentIndex = index;
      _currentQuote = _filteredQuotes[index];
      
      // Check if quote is favorite
      if (_currentQuote!.id != null) {
        final isFavorite = await _databaseService.isQuoteFavorite(_currentQuote!.id!);
        _currentQuote = _currentQuote!.copyWith(isFavorite: isFavorite);
      }
      
      await _preferencesService.setCurrentQuoteIndex(_currentIndex);
      notifyListeners();
    } catch (e) {
      _setError('Failed to set current quote: $e');
    }
  }

  // Refresh quotes
  Future<void> refresh() async {
    await loadQuotes();
    await loadFavorites();
    await loadCurrentQuote();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _updateQuoteInLists(Quote updatedQuote) {
    // Update in main quotes list
    final mainIndex = _quotes.indexWhere((q) => q.id == updatedQuote.id);
    if (mainIndex != -1) {
      _quotes[mainIndex] = updatedQuote;
    }
    
    // Update in filtered quotes list
    final filteredIndex = _filteredQuotes.indexWhere((q) => q.id == updatedQuote.id);
    if (filteredIndex != -1) {
      _filteredQuotes[filteredIndex] = updatedQuote;
    }
  }
}
