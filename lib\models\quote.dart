class Quote {
  final int? id;
  final String text;
  final String author;
  final String category;
  final bool isFavorite;
  final DateTime? dateAdded;

  const Quote({
    this.id,
    required this.text,
    required this.author,
    required this.category,
    this.isFavorite = false,
    this.dateAdded,
  });

  Quote copyWith({
    int? id,
    String? text,
    String? author,
    String? category,
    bool? isFavorite,
    DateTime? dateAdded,
  }) {
    return Quote(
      id: id ?? this.id,
      text: text ?? this.text,
      author: author ?? this.author,
      category: category ?? this.category,
      isFavorite: isFavorite ?? this.isFavorite,
      dateAdded: dateAdded ?? this.dateAdded,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'text': text,
      'author': author,
      'category': category,
      'isFavorite': isFavorite ? 1 : 0,
      'dateAdded': dateAdded?.millisecondsSinceEpoch,
    };
  }

  factory Quote.fromMap(Map<String, dynamic> map) {
    return Quote(
      id: map['id']?.toInt(),
      text: map['text'] ?? '',
      author: map['author'] ?? '',
      category: map['category'] ?? '',
      isFavorite: (map['isFavorite'] ?? 0) == 1,
      dateAdded: map['dateAdded'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['dateAdded'])
          : null,
    );
  }

  factory Quote.fromJson(Map<String, dynamic> json) {
    return Quote(
      id: json['id'],
      text: json['text'] ?? json['quote'] ?? '',
      author: json['author'] ?? '',
      category: json['category'] ?? 'general',
      isFavorite: json['isFavorite'] ?? false,
      dateAdded: json['dateAdded'] != null 
          ? DateTime.parse(json['dateAdded'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'author': author,
      'category': category,
      'isFavorite': isFavorite,
      'dateAdded': dateAdded?.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'Quote(id: $id, text: $text, author: $author, category: $category, isFavorite: $isFavorite)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Quote &&
        other.id == id &&
        other.text == text &&
        other.author == author &&
        other.category == category &&
        other.isFavorite == isFavorite;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        text.hashCode ^
        author.hashCode ^
        category.hashCode ^
        isFavorite.hashCode;
  }
}
