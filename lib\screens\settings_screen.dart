import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../providers/settings_provider.dart';
import '../widgets/custom_app_bar.dart';
import '../constants/app_constants.dart';
import '../utils/app_utils.dart';
import '../models/quote.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen>
    with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SettingsProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: const CustomAppBar(
        title: AppStrings.settings,
        showBackButton: false,
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        children: [
          // Theme Section
          _buildSectionHeader('Appearance'),
          _buildThemeSettings(),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Notifications Section
          _buildSectionHeader('Notifications'),
          _buildNotificationSettings(),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // About Section
          _buildSectionHeader('About'),
          _buildAboutSettings(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.smallPadding),
      child: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
          color: theme.colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildThemeSettings() {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Card(
          child: Column(
            children: [
              ListTile(
                leading: Icon(
                  themeProvider.isDarkMode ? Icons.dark_mode : Icons.light_mode,
                ),
                title: const Text(AppStrings.darkMode),
                subtitle: Text(
                  themeProvider.isDarkMode ? 'Dark theme enabled' : 'Light theme enabled',
                ),
                trailing: Switch(
                  value: themeProvider.themeMode == ThemeMode.dark,
                  onChanged: (value) {
                    themeProvider.setThemeMode(
                      value ? ThemeMode.dark : ThemeMode.light,
                    );
                    AppUtils.lightHaptic();
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNotificationSettings() {
    return Consumer<SettingsProvider>(
      builder: (context, settingsProvider, child) {
        return Card(
          child: Column(
            children: [
              ListTile(
                leading: const Icon(Icons.notifications),
                title: const Text(AppStrings.notifications),
                subtitle: Text(
                  settingsProvider.notificationsEnabled
                      ? 'Daily quotes notifications enabled'
                      : 'Notifications disabled',
                ),
                trailing: Switch(
                  value: settingsProvider.notificationsEnabled,
                  onChanged: (value) async {
                    await settingsProvider.setNotificationsEnabled(value);
                    AppUtils.lightHaptic();
                    
                    if (mounted) {
                      AppUtils.showSnackBar(
                        context,
                        value 
                            ? 'Notifications enabled'
                            : 'Notifications disabled',
                        icon: value ? Icons.notifications : Icons.notifications_off,
                      );
                    }
                  },
                ),
              ),
              
              if (settingsProvider.notificationsEnabled) ...[
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(Icons.schedule),
                  title: const Text(AppStrings.notificationTime),
                  subtitle: Text(
                    'Daily quote at ${AppUtils.formatTime(settingsProvider.notificationTime)}',
                  ),
                  trailing: const Icon(Icons.chevron_right),
                  onTap: () => _showTimePicker(settingsProvider),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildAboutSettings() {
    final theme = Theme.of(context);
    
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.info),
            title: const Text(AppStrings.about),
            subtitle: const Text('Learn more about Daily Quotes'),
            trailing: const Icon(Icons.chevron_right),
            onTap: _showAboutDialog,
          ),
          
          const Divider(height: 1),
          
          ListTile(
            leading: const Icon(Icons.star),
            title: const Text('Rate App'),
            subtitle: const Text('Rate us on the app store'),
            trailing: const Icon(Icons.chevron_right),
            onTap: _rateApp,
          ),
          
          const Divider(height: 1),
          
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('Share App'),
            subtitle: const Text('Share with friends and family'),
            trailing: const Icon(Icons.chevron_right),
            onTap: _shareApp,
          ),
          
          const Divider(height: 1),
          
          ListTile(
            leading: const Icon(Icons.code),
            title: const Text(AppStrings.version),
            subtitle: const Text(AppConstants.appVersion),
          ),
        ],
      ),
    );
  }

  void _showTimePicker(SettingsProvider settingsProvider) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: settingsProvider.notificationTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            timePickerTheme: TimePickerThemeData(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != settingsProvider.notificationTime) {
      await settingsProvider.setNotificationTime(picked);
      
      if (mounted) {
        AppUtils.showSnackBar(
          context,
          'Notification time updated to ${AppUtils.formatTime(picked)}',
          icon: Icons.schedule,
        );
      }
    }
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: AppConstants.appName,
      applicationVersion: AppConstants.appVersion,
      applicationIcon: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Icon(
          Icons.format_quote,
          color: Colors.white,
          size: 32,
        ),
      ),
      children: [
        const Text(
          'Daily Quotes brings you inspirational quotes every day to motivate and inspire you. '
          'Browse through different categories, save your favorites, and share quotes with others.',
        ),
        const SizedBox(height: 16),
        const Text(
          'Features:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const Text('• Daily inspirational quotes'),
        const Text('• Browse by categories'),
        const Text('• Save favorite quotes'),
        const Text('• Share quotes with others'),
        const Text('• Daily notifications'),
        const Text('• Dark and light themes'),
      ],
    );
  }

  void _rateApp() {
    // Implement app store rating
    AppUtils.showSnackBar(
      context,
      'Thank you for your feedback!',
      icon: Icons.star,
    );
  }

  void _shareApp() async {
    await AppUtils.shareQuote(
      // Create a dummy quote for sharing the app
      const Quote(
        text: 'Check out this amazing Daily Quotes app!',
        author: 'Daily Quotes App',
        category: 'App',
      ),
    );
  }
}
