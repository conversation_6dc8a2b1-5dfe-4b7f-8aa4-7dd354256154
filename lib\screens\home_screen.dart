import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quote_provider.dart';
import '../providers/theme_provider.dart';
import '../widgets/quote_card.dart';
import '../widgets/loading_widget.dart';
import '../widgets/empty_state_widget.dart';
import '../widgets/custom_app_bar.dart';
import '../constants/app_constants.dart';
import '../utils/app_utils.dart';
import '../models/quote.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with AutomaticKeepAliveClientMixin {
  late PageController _pageController;
  int _currentPage = 0;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<QuoteProvider>().initialize();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
  }

  Future<void> _onRefresh() async {
    await context.read<QuoteProvider>().refresh();
  }

  void _onFavoriteToggle(Quote quote) async {
    await context.read<QuoteProvider>().toggleFavorite(quote);
    
    if (mounted) {
      final isFavorite = await context.read<QuoteProvider>().isQuoteFavorite(quote);
      AppUtils.showSnackBar(
        context,
        isFavorite ? AppStrings.addedToFavorites : AppStrings.removedFromFavorites,
        icon: isFavorite ? Icons.favorite : Icons.favorite_border,
      );
    }
  }

  void _onShareQuote(Quote quote) async {
    final RenderBox? box = context.findRenderObject() as RenderBox?;
    final Rect sharePositionOrigin = box != null
        ? box.localToGlobal(Offset.zero) & box.size
        : Rect.zero;
    
    await AppUtils.shareQuote(quote, sharePositionOrigin: sharePositionOrigin);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final themeProvider = context.watch<ThemeProvider>();
    
    return Scaffold(
      body: Consumer<QuoteProvider>(
        builder: (context, quoteProvider, child) {
          if (quoteProvider.isLoading && quoteProvider.quotes.isEmpty) {
            return const LoadingWidget(message: 'Loading quotes...');
          }

          if (quoteProvider.error != null && quoteProvider.quotes.isEmpty) {
            return ErrorStateWidget(
              message: quoteProvider.error!,
              onActionPressed: _onRefresh,
            );
          }

          if (quoteProvider.quotes.isEmpty) {
            return const EmptyStateWidget(
              icon: Icons.format_quote,
              title: 'No Quotes Available',
              message: 'Check your internet connection and try again.',
            );
          }

          return CustomScrollView(
            slivers: [
              // Custom App Bar with greeting
              SliverAppBar(
                expandedHeight: 120,
                floating: true,
                pinned: false,
                backgroundColor: theme.colorScheme.surface,
                flexibleSpace: FlexibleSpaceBar(
                  background: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: AppUtils.getRandomGradient(),
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: SafeArea(
                      child: Padding(
                        padding: const EdgeInsets.all(AppConstants.defaultPadding),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Text(
                              AppUtils.getGreeting(),
                              style: theme.textTheme.headlineSmall?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Here\'s your daily inspiration',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: Colors.white.withOpacity(0.9),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                actions: [
                  IconButton(
                    onPressed: () => themeProvider.toggleTheme(),
                    icon: Icon(
                      themeProvider.isDarkMode 
                          ? Icons.light_mode 
                          : Icons.dark_mode,
                    ),
                  ),
                ],
              ),

              // Daily Quote Section
              if (quoteProvider.currentQuote != null)
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Quote of the Day',
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: AppConstants.smallPadding),
                        QuoteCard(
                          quote: quoteProvider.currentQuote!,
                          onFavoriteToggle: () => _onFavoriteToggle(quoteProvider.currentQuote!),
                          onShare: () => _onShareQuote(quoteProvider.currentQuote!),
                        ),
                      ],
                    ),
                  ),
                ),

              // Recent Quotes Section
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.defaultPadding,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Explore Quotes',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          // Navigate to all quotes or categories
                        },
                        child: const Text('See All'),
                      ),
                    ],
                  ),
                ),
              ),

              // Quotes List
              SliverPadding(
                padding: const EdgeInsets.only(
                  bottom: AppConstants.defaultPadding + 80, // Account for FAB and bottom nav
                ),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final quote = quoteProvider.filteredQuotes[index];
                      return QuoteCard(
                        quote: quote,
                        animationIndex: index,
                        isCompact: true,
                        onTap: () {
                          // Navigate to quote detail or set as current
                          context.read<QuoteProvider>().setCurrentQuoteByIndex(index);
                        },
                        onFavoriteToggle: () => _onFavoriteToggle(quote),
                        onShare: () => _onShareQuote(quote),
                      );
                    },
                    childCount: quoteProvider.filteredQuotes.length,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
