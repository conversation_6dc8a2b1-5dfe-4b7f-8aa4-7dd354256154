import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quote_provider.dart';
import '../widgets/quote_card.dart';
import '../widgets/loading_widget.dart';
import '../widgets/empty_state_widget.dart';
import '../widgets/custom_app_bar.dart';
import '../constants/app_constants.dart';
import '../utils/app_utils.dart';
import '../models/quote.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen>
    with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<QuoteProvider>().loadFavorites();
    });
  }

  Future<void> _onRefresh() async {
    await context.read<QuoteProvider>().loadFavorites();
  }

  void _onFavoriteToggle(Quote quote) async {
    await context.read<QuoteProvider>().toggleFavorite(quote);
    
    if (mounted) {
      AppUtils.showSnackBar(
        context,
        AppStrings.removedFromFavorites,
        icon: Icons.favorite_border,
      );
    }
  }

  void _onShareQuote(Quote quote) async {
    final RenderBox? box = context.findRenderObject() as RenderBox?;
    final Rect sharePositionOrigin = box != null
        ? box.localToGlobal(Offset.zero) & box.size
        : Rect.zero;
    
    await AppUtils.shareQuote(quote, sharePositionOrigin: sharePositionOrigin);
  }

  void _onExploreQuotes() {
    // Navigate to home or categories
    DefaultTabController.of(context)?.animateTo(0); // Navigate to home tab
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: CustomAppBar(
        title: AppStrings.favorites,
        showBackButton: false,
        actions: [
          Consumer<QuoteProvider>(
            builder: (context, quoteProvider, child) {
              if (quoteProvider.favoriteQuotes.isNotEmpty) {
                return PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'refresh':
                        _onRefresh();
                        break;
                      case 'clear_all':
                        _showClearAllDialog();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'refresh',
                      child: Row(
                        children: [
                          Icon(Icons.refresh),
                          SizedBox(width: 8),
                          Text('Refresh'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'clear_all',
                      child: Row(
                        children: [
                          Icon(Icons.clear_all),
                          SizedBox(width: 8),
                          Text('Clear All'),
                        ],
                      ),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      body: Consumer<QuoteProvider>(
        builder: (context, quoteProvider, child) {
          if (quoteProvider.isLoading && quoteProvider.favoriteQuotes.isEmpty) {
            return const LoadingWidget(message: 'Loading favorites...');
          }

          if (quoteProvider.favoriteQuotes.isEmpty) {
            return NoFavoritesWidget(
              onExplore: _onExploreQuotes,
            );
          }

          return RefreshIndicator(
            onRefresh: _onRefresh,
            child: Column(
              children: [
                // Header with count
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer.withOpacity(0.3),
                    border: Border(
                      bottom: BorderSide(
                        color: theme.colorScheme.outline.withOpacity(0.2),
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.favorite,
                        color: Colors.red,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${quoteProvider.favoriteQuotes.length} favorite quotes',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),

                // Favorites List
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.only(
                      bottom: AppConstants.defaultPadding + 80, // Account for bottom nav
                    ),
                    itemCount: quoteProvider.favoriteQuotes.length,
                    itemBuilder: (context, index) {
                      final quote = quoteProvider.favoriteQuotes[index];
                      return Dismissible(
                        key: Key('favorite_${quote.id}'),
                        direction: DismissDirection.endToStart,
                        background: Container(
                          alignment: Alignment.centerRight,
                          padding: const EdgeInsets.only(right: AppConstants.defaultPadding),
                          margin: const EdgeInsets.symmetric(
                            horizontal: AppConstants.defaultPadding,
                            vertical: AppConstants.smallPadding,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                          ),
                          child: const Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.delete,
                                color: Colors.white,
                                size: 24,
                              ),
                              SizedBox(height: 4),
                              Text(
                                'Remove',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        confirmDismiss: (direction) async {
                          return await AppUtils.showConfirmationDialog(
                            context,
                            title: 'Remove from Favorites',
                            message: 'Are you sure you want to remove this quote from favorites?',
                            confirmText: 'Remove',
                            cancelText: 'Cancel',
                          );
                        },
                        onDismissed: (direction) {
                          _onFavoriteToggle(quote);
                        },
                        child: QuoteCard(
                          quote: quote.copyWith(isFavorite: true),
                          animationIndex: index,
                          onTap: () {
                            // Navigate to quote detail or set as current
                          },
                          onFavoriteToggle: () => _onFavoriteToggle(quote),
                          onShare: () => _onShareQuote(quote),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _showClearAllDialog() async {
    final confirmed = await AppUtils.showConfirmationDialog(
      context,
      title: 'Clear All Favorites',
      message: 'Are you sure you want to remove all quotes from favorites? This action cannot be undone.',
      confirmText: 'Clear All',
      cancelText: 'Cancel',
    );

    if (confirmed && mounted) {
      final quoteProvider = context.read<QuoteProvider>();
      for (final quote in quoteProvider.favoriteQuotes) {
        await quoteProvider.toggleFavorite(quote);
      }
      
      AppUtils.showSnackBar(
        context,
        'All favorites cleared',
        icon: Icons.clear_all,
      );
    }
  }
}
